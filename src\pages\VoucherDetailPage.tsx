import React, { useState, useRef, useCallback } from 'react'
import { Page } from 'zmp-ui'
import { closeApp } from 'zmp-sdk/apis'
import { Check } from 'lucide-react'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import Copy from '@/assets/icons/Copy.svg?react'
import voucherBackground from '@/assets/images/voucherBackground.webp'
import btaskeeIcon from '@/assets/icons/btaskee_icon.webp'
import { ScrollArea } from '@/shared/components/ui/scroll-area'

const voucherData = {
  title: 'Discount 20.000đ for Grocery Assistant',
  code: 'FGH6FV8Y',
  expiryDate: '23:59, 30/07/2025',
  discount: '10%',
  maxValue: '100.000 VNĐ',
  minOrder: '300.000 VNĐ',
  details:
    'Enjoy 20.000đ for your next Grocery Assistant Service booking. Thank you for choosing bTaskee!',
  terms: [
    'Promotion code only applied for 01 booking.',
    'This promotion cannot be used together with other promotions.',
    'Not refundable and exchangeable.',
    'Promotion code only has an expiry date of 30 days since redemption.',
  ],
}

function VoucherDetailPage() {
  const [copied, setCopied] = useState(false)
  const [scrollAreaHeight, setScrollAreaHeight] = useState(50) // Height in percentage (50% to 65%)
  const [touchStart, setTouchStart] = useState<{
    y: number
    time: number
    scrollTop: number
    initialHeight: number
  } | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isSnapping, setIsSnapping] = useState(false)
  const [hasExpandedToMax, setHasExpandedToMax] = useState(false) // Track if we've reached max height during this gesture
  const [hasScrolledToTop, setHasScrolledToTop] = useState(false) // Track if we've scrolled to top during this gesture

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const scrollViewportRef = useRef<HTMLDivElement>(null)

  // Constants for scroll area behavior
  const MIN_HEIGHT = 50 // Minimum height percentage
  const MAX_HEIGHT = 65 // Maximum height percentage

  // Get current scroll position
  const getScrollTop = useCallback(() => {
    const scrollArea = scrollViewportRef.current
    if (!scrollArea) return 0
    // Find the viewport element within the ScrollArea
    const viewport = scrollArea.querySelector(
      '[data-radix-scroll-area-viewport]'
    ) as HTMLElement
    return viewport ? viewport.scrollTop : 0
  }, [])

  // Set scroll position
  const setScrollTop = useCallback((scrollTop: number) => {
    const scrollArea = scrollViewportRef.current
    if (!scrollArea) return
    // Find the viewport element within the ScrollArea
    const viewport = scrollArea.querySelector(
      '[data-radix-scroll-area-viewport]'
    ) as HTMLElement
    if (viewport) {
      viewport.scrollTop = scrollTop
    }
  }, [])

  // Check if content can scroll (has overflow)
  const canScroll = useCallback(() => {
    const scrollArea = scrollViewportRef.current
    if (!scrollArea) return false
    // Find the viewport element within the ScrollArea
    const viewport = scrollArea.querySelector(
      '[data-radix-scroll-area-viewport]'
    ) as HTMLElement
    if (!viewport) return false
    return viewport.scrollHeight > viewport.clientHeight
  }, [])

  // Handle touch start
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      const touch = e.touches[0]
      const currentScrollTop = getScrollTop()

      setTouchStart({
        y: touch.clientY,
        time: Date.now(),
        scrollTop: currentScrollTop,
        initialHeight: scrollAreaHeight,
      })
      setIsDragging(true)
      setHasExpandedToMax(false)
      setHasScrolledToTop(false)
    },
    [getScrollTop, scrollAreaHeight]
  )

  // Handle touch move
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!touchStart || !isDragging) return

      const touch = e.touches[0]
      const deltaY = touch.clientY - touchStart.y
      const currentScrollTop = getScrollTop()
      const containerHeight = window.innerHeight

      // Convert pixel movement to percentage (more responsive)
      const heightChangePercentage = (Math.abs(deltaY) / containerHeight) * 30 // Adjust sensitivity

      if (deltaY < 0) {
        // Dragging UP
        if (touchStart.initialHeight < MAX_HEIGHT && !hasExpandedToMax) {
          // PHASE 1: Expand scroll area to maximum height first
          e.preventDefault() // Block all scrolling
          const newHeight = Math.min(
            MAX_HEIGHT,
            touchStart.initialHeight + heightChangePercentage
          )
          setScrollAreaHeight(newHeight)

          // Mark that we've reached max height
          if (newHeight >= MAX_HEIGHT) {
            setHasExpandedToMax(true)
          }
        } else if (
          (touchStart.initialHeight >= MAX_HEIGHT || hasExpandedToMax) &&
          scrollAreaHeight >= MAX_HEIGHT
        ) {
          // PHASE 2: Allow scrolling only after reaching max height
          // Don't prevent default - allow normal scroll behavior
          return
        }
      } else if (deltaY > 0) {
        // Dragging DOWN
        if (touchStart.initialHeight >= MAX_HEIGHT || hasExpandedToMax) {
          if (
            (touchStart.scrollTop > 0 || currentScrollTop > 0) &&
            !hasScrolledToTop
          ) {
            // PHASE 1: Scroll to top first
            // Don't prevent default - allow normal scroll behavior

            // Check if we've reached the top
            if (currentScrollTop <= 0) {
              setHasScrolledToTop(true)
            }
            return
          } else if (currentScrollTop <= 0 || hasScrolledToTop) {
            // PHASE 2: Shrink the area only after scrolling to top
            e.preventDefault() // Block scrolling
            const newHeight = Math.max(
              MIN_HEIGHT,
              scrollAreaHeight - heightChangePercentage
            )
            setScrollAreaHeight(newHeight)
          }
        }
      }
    },
    [
      touchStart,
      isDragging,
      scrollAreaHeight,
      getScrollTop,
      canScroll,
      hasExpandedToMax,
      hasScrolledToTop,
    ]
  )

  // Handle touch end
  const handleTouchEnd = useCallback(() => {
    setTouchStart(null)
    setIsDragging(false)
    setIsSnapping(true)
    setHasExpandedToMax(false)
    setHasScrolledToTop(false)

    // Snap to nearest height (50% or 65%)
    const midPoint = (MIN_HEIGHT + MAX_HEIGHT) / 2
    if (scrollAreaHeight < midPoint) {
      setScrollAreaHeight(MIN_HEIGHT)
    } else {
      setScrollAreaHeight(MAX_HEIGHT)
    }

    // Reset snapping state after transition
    setTimeout(() => setIsSnapping(false), 300)
  }, [scrollAreaHeight])

  const handleBackClick = () => {
    closeApp()
  }

  const handleCopyCode = () => {
    navigator.clipboard.writeText(voucherData.code)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <Page className="relative text-black flex flex-col items-center justify-center min-h-screen overflow-hidden bg-white">
      {/* Top half background */}
      <div
        className="absolute top-0 left-0 w-full h-1/2 bg-cover bg-center"
        style={{ backgroundImage: `url(${voucherBackground})` }}
      />

      {/* Content */}
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe pb-safe">
        {/* Top Navigation */}
        <div className="flex items-start justify-start pointer-events-auto z-20 mb-4 px-4">
          <button
            onClick={handleBackClick}
            className="bg-[#F8F9FB] rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
          >
            <LeftArrow className="scale-110 -translate-x-0.5" />
          </button>
        </div>

        {/* Spacer to push scroll area to bottom */}
        <div className="flex-1" />

        {/* Scrollable content at bottom */}
        <div className="top-0 left-0 right-0 h-24 bg-gradient-to-t from-white to-transparent pointer-events-none z-10" />
        <div
          ref={scrollAreaRef}
          className={`bg-white px-4 scrollbar-none ${
            isSnapping ? 'transition-all duration-300 ease-out' : ''
          }`}
          style={{ height: `${scrollAreaHeight}%` }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <ScrollArea ref={scrollViewportRef} className="h-full scrollbar-none">
            {/* Voucher Title */}
            <div className="flex items-start gap-3 mt-5">
              <img src={btaskeeIcon} alt="bTaskee Icon" className="w-12 h-12" />
              <div className="flex-1">
                <h1 className="text-[18px] font-bold text-[#212935]">
                  {voucherData.title}
                </h1>
              </div>
            </div>

            {/* Voucher Code Section */}
            <div className="my-10">
              <div className="flex items-center justify-between bg-[#FBFCFF] rounded-lg py-4 pl-3">
                <div className="flex-1">
                  <div className="text-[#FF8228] font-bold text-[18px] tracking-wider">
                    {voucherData.code}
                  </div>
                </div>
                <button
                  onClick={handleCopyCode}
                  className="p-2 text-green-600 rounded-lg transition-colors"
                >
                  {copied ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    <Copy className="w-6 h-6" />
                  )}
                </button>
              </div>
              <div className="text-[#636F81] font-medium text-[14px] mt-3">
                Ngày hết hạn:{' '}
                <span className="font-semibold text-[#212935]">
                  {voucherData.expiryDate}
                </span>
              </div>
            </div>

            {/* Reward Details */}
            <div className="mb-3 text-[14px] text-[#212935]">
              <h3 className="font-bold mb-2">Reward details</h3>
              <p className="font-medium leading-relaxed">
                {voucherData.details}
              </p>
            </div>

            {/* Terms and Conditions */}
            <div className="mb-10 text-[14px] text-[#212935]">
              <h3 className="font-bold mb-2">Terms and Conditions</h3>
              <div className="space-y-2">
                {voucherData.terms.map((term, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-2 text-gray-700"
                  >
                    <span className="flex-shrink-0">•</span>
                    <span className="leading-relaxed">{term}</span>
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
        </div>
        <div className="px-4 pt-3 bg-white">
          <button
            className="w-full py-3 bg-[#E4EAF0] text-[#1BB55C] font-semibold rounded-xl text-[16px]"
            onClick={() => {
              // handle immediate use
            }}
          >
            Sử dụng ngay
          </button>
        </div>
      </div>
    </Page>
  )
}

export default VoucherDetailPage
