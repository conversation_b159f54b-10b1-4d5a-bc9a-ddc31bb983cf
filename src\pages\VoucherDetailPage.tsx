import React, { useState } from 'react'
import { Page } from 'zmp-ui'
import { closeApp } from 'zmp-sdk/apis'
import { Check } from 'lucide-react'
import LeftArrow from '@/assets/icons/LeftArrow.svg?react'
import Copy from '@/assets/icons/Copy.svg?react'
import voucherBackground from '@/assets/images/voucherBackground.webp'
import btaskeeIcon from '@/assets/icons/btaskee_icon.webp'
import { ScrollArea } from '@/shared/components/ui/scroll-area'

const voucherData = {
  title: 'Discount 20.000đ for Grocery Assistant',
  code: 'FGH6FV8Y',
  expiryDate: '23:59, 30/07/2025',
  discount: '10%',
  maxValue: '100.000 VNĐ',
  minOrder: '300.000 VNĐ',
  details:
    'Enjoy 20.000đ for your next Grocery Assistant Service booking. Thank you for choosing bTaskee!',
  terms: [
    'Promotion code only applied for 01 booking.',
    'This promotion cannot be used together with other promotions.',
    'Not refundable and exchangeable.',
    'Promotion code only has an expiry date of 30 days since redemption.',
  ],
}

function VoucherDetailPage() {
  const [copied, setCopied] = useState(false)

  const handleBackClick = () => {
    closeApp()
  }

  const handleCopyCode = () => {
    navigator.clipboard.writeText(voucherData.code)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <Page className="relative text-black flex flex-col items-center justify-center min-h-screen overflow-hidden bg-white">
      {/* Top half background */}
      <div
        className="absolute top-0 left-0 w-full h-1/2 bg-cover bg-center"
        style={{ backgroundImage: `url(${voucherBackground})` }}
      />

      {/* Content */}
      <div className="font-montserrat relative z-10 flex flex-col w-full h-full pt-safe pb-safe">
        {/* Top Navigation */}
        <div className="flex items-start justify-start pointer-events-auto z-20 mb-4 px-4">
          <button
            onClick={handleBackClick}
            className="bg-[#F8F9FB] rounded-lg p-2 aspect-square w-9 flex items-center justify-center"
          >
            <LeftArrow className="scale-110 -translate-x-0.5" />
          </button>
        </div>

        {/* Spacer to push scroll area to bottom */}
        <div className="flex-1" />

        {/* Scrollable content at bottom */}
        <div className="top-0 left-0 right-0 h-24 bg-gradient-to-t from-white to-transparent pointer-events-none z-10" />
        <ScrollArea className="h-[50%] bg-white px-4 scrollbar-none">
          {/* Voucher Title */}
          <div className="flex items-start gap-3 mt-5">
            <img src={btaskeeIcon} alt="bTaskee Icon" className="w-12 h-12" />
            <div className="flex-1">
              <h1 className="text-[18px] font-bold text-[#212935]">
                {voucherData.title}
              </h1>
            </div>
          </div>

          {/* Voucher Code Section */}
          <div className="my-10">
            <div className="flex items-center justify-between bg-[#FBFCFF] rounded-lg py-4 pl-3">
              <div className="flex-1">
                <div className="text-[#FF8228] font-bold text-[18px] tracking-wider">
                  {voucherData.code}
                </div>
              </div>
              <button
                onClick={handleCopyCode}
                className="p-2 text-green-600 rounded-lg transition-colors"
              >
                {copied ? (
                  <Check className="w-6 h-6" />
                ) : (
                  <Copy className="w-6 h-6" />
                )}
              </button>
            </div>
            <div className="text-[#636F81] font-medium text-[14px] mt-3">
              Ngày hết hạn:{' '}
              <span className="font-semibold text-[#212935]">
                {voucherData.expiryDate}
              </span>
            </div>
          </div>

          {/* Reward Details */}
          <div className="mb-3 text-[14px] text-[#212935]">
            <h3 className="font-bold mb-2">Reward details</h3>
            <p className="font-medium leading-relaxed">{voucherData.details}</p>
          </div>

          {/* Terms and Conditions */}
          <div className="mb-10 text-[14px] text-[#212935]">
            <h3 className="font-bold mb-2">Terms and Conditions</h3>
            <div className="space-y-2">
              {voucherData.terms.map((term, index) => (
                <div
                  key={index}
                  className="flex items-start gap-2 text-gray-700"
                >
                  <span className="flex-shrink-0">•</span>
                  <span className="leading-relaxed">{term}</span>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>
        <div className="px-4 pt-3 bg-white">
          <button
            className="w-full py-3 bg-[#E4EAF0] text-[#1BB55C] font-semibold rounded-xl text-[16px]"
            onClick={() => {
              // handle immediate use
            }}
          >
            Sử dụng ngay
          </button>
        </div>
      </div>
    </Page>
  )
}

export default VoucherDetailPage
